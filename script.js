// 替换为你的真实Key
const jsapiKey = '835a3d283857c293653c3a6a112f8cb2';
const webServiceKey = '3a5b5a18dca4b2f6fe0b489303f2e6ea';

// 默认中心，若定位失败则以这里为准（广州）
const defaultCenter = [113.35, 23.04];
const initZoom = 12;
let currentCenterLngLat = defaultCenter.slice();

const map = new AMap.Map('amap-canvas', {
  zoom: initZoom,
  center: defaultCenter,
  resizeEnable: true,
  dragEnable: true,
  scrollWheel: true,
  doubleClickZoom: true,
});
let markers = [];
function clearMarkers() {
  markers.forEach(mk=>map.remove(mk));
  markers = [];
}
function getDotSize(zoom) { return Math.max(8, 26 - 3*(zoom-11)); }
function createRedDotMarker(poi, size) {
  let [lng, lat] = poi.location.split(',').map(Number);
  let dom = document.createElement('div'); dom.className = 'my-red-dot';
  dom.style.width = dom.style.height = size+"px";
  let marker = new AMap.Marker({
    position:[lng,lat],
    offset: new AMap.Pixel(-size/2, -size/2),
    content: dom, anchor:'center', map: map
  });

  // 使用高德地图的事件系统
  marker.on('click', function(e) {
    console.log('Marker被点击了！', poi.name); // 调试信息
    e.stopPropagation(); // 防止事件冒泡
    showDraggablePopup(poi, lng, lat);
  });

  // 同时也保留DOM元素的点击事件作为备用
  dom.onclick = function(e) {
    console.log('DOM红点被点击了！', poi.name); // 调试信息
    e.stopPropagation(); // 防止事件冒泡
    showDraggablePopup(poi, lng, lat);
  };
  marker._customDom = dom; marker._size = size; marker._poi = poi;
  return marker;
}
function updateDotMarkerSizeOnZoom(curZoom) {
  let size = getDotSize(curZoom);
  markers.forEach(mk=>{
    mk._customDom.style.width = mk._customDom.style.height = size+"px";
    mk.setOffset(new AMap.Pixel(-size/2, -size/2));
  });
}
function getUserKeywords() {
  let v = inputElem.value.trim();
  return v ? v : '汽修';
}
function getUserRadiusKm() {
  let val = parseFloat(rangeElem.value);
  if(isNaN(val) || val<0.5) val = 0.5;
  if(val > 100) val = 100;
  rangeElem.value = val;
  return val;
}
async function searchNearby(centerLng, centerLat, inputKeywords, inputRadiusKm) {
  let kwStrings = inputKeywords.split(/\s*,\s*|\s+/).filter(kw=>kw.trim());
  if (kwStrings.length === 0) kwStrings = ['汽修'];
  let radiusMeters = Math.max(500, Math.round(inputRadiusKm*1000));
  let all = [];
  for(let kw of kwStrings) {
    let url = `https://restapi.amap.com/v3/place/around?key=${webServiceKey}&keywords=${encodeURIComponent(kw)}&location=${centerLng},${centerLat}&radius=${radiusMeters}&sortrule=distance&offset=25&page=1`;
    let res = await fetch(url);
    let data=await res.json();
    if(data.pois) all = all.concat(data.pois.filter(p=>p.location && p.name));
  }
  let m = {};
  all.forEach(x=>m[x.id]=x);
  return Object.values(m);
}
async function doSearchAndRender(centerLng, centerLat) {
  let keywords = getUserKeywords();
  let radius = getUserRadiusKm();
  clearMarkers();
  let pois = await searchNearby(centerLng, centerLat, keywords, radius);
  let size = getDotSize(map.getZoom());
  markers = pois.map(p=>createRedDotMarker(p, size));
}
// 拖拽刷新
let dragTimer = null;
const hintElem = document.querySelector(".countdown-hint");
function startDragCountdown(){
  let remain = 1;
  clearTimeout(dragTimer);
  let showCD = ()=>{
    hintElem.style.display='block';
    hintElem.textContent = `地图已移动，${remain}s后自动检索（关键词：${getUserKeywords()}，范围：${getUserRadiusKm()}公里）...`;
  }
  showCD();
  dragTimer = setInterval(()=>{
    remain--;
    if(remain<=0) {
      clearInterval(dragTimer);
      hintElem.style.display='none';
      triggerCurrentMapCenterSearch();
    } else { showCD();}
  },1000);
}
function cancelDragCountdown(){ clearTimeout(dragTimer); hintElem.style.display='none'; }
map.on('moveend', ()=>{ startDragCountdown(); });
map.on('movestart', ()=>{ cancelDragCountdown(); });
map.on('zoomchange', ()=> { let curZoom = map.getZoom(); updateDotMarkerSizeOnZoom(curZoom); });
function triggerCurrentMapCenterSearch(){
  let c = map.getCenter();
  doSearchAndRender(c.lng, c.lat);
}
// 输入按钮事件
const inputElem = document.querySelector('.keyword-input');
const rangeElem = document.querySelector('.range-input');
const searchBtn = document.querySelector('.search-btn');
document.querySelector('.refresh-btn').onclick = ()=>{
  cancelDragCountdown();
  triggerCurrentMapCenterSearch();
};
inputElem.addEventListener('keydown', e=>{
  if(e.key==="Enter"){ searchBtn.click(); }
});
rangeElem.addEventListener('keydown', e=>{
  if(e.key==="Enter"){ searchBtn.click(); }
});
searchBtn.onclick = ()=>{
  let kw = getUserKeywords();
  let radius = getUserRadiusKm();
  inputElem.value = kw;
  rangeElem.value = radius;
  cancelDragCountdown();
  triggerCurrentMapCenterSearch();
};
rangeElem.addEventListener('change',function(){
  let r = getUserRadiusKm();
  rangeElem.value = r;
});
// ------- 首次定位查找 -------
function firstSearchWithGeolocation(){
  if(navigator.geolocation){
    navigator.geolocation.getCurrentPosition(
      function(position){
        let lng = position.coords.longitude;
        let lat = position.coords.latitude;
        currentCenterLngLat = [lng, lat];
        map.setCenter(currentCenterLngLat);
        setTimeout(()=>{ doSearchAndRender(currentCenterLngLat[0], currentCenterLngLat[1]); },150);
        showUserLocationMarker(currentCenterLngLat);
      },
      function(err){
        map.setCenter(defaultCenter);
        doSearchAndRender(defaultCenter[0], defaultCenter[1]);
      },
      {enableHighAccuracy:true, timeout:6000, maximumAge:60000}
    );
  }else{
    map.setCenter(defaultCenter);
    doSearchAndRender(defaultCenter[0], defaultCenter[1]);
  }
}
// ------ 回到当前位置按钮 ------
document.querySelector('.my-location-btn').onclick = function() {
  const btn = this;
  if(navigator.geolocation) {
    btn.disabled = true;
    btn.style.opacity = "0.6";
    btn.textContent = '⌛';
    navigator.geolocation.getCurrentPosition(
      function(pos) {
        let lng = pos.coords.longitude;
        let lat = pos.coords.latitude;
        map.setCenter([lng, lat]);
        setTimeout(() => { doSearchAndRender(lng, lat); }, 150);
        showUserLocationMarker([lng, lat]);
        resetMyLocationBtn();
      },
      function(err) {
        alert("定位失败，请检查浏览器定位设置或网络。");
        resetMyLocationBtn();
      },
      {enableHighAccuracy:true, timeout:8000, maximumAge:60000}
    );
  } else {
    alert("本浏览器不支持地理位置。");
  }
};
function resetMyLocationBtn() {
  let btn = document.querySelector('.my-location-btn');
  btn.disabled = false;
  btn.style.opacity = "";
  btn.textContent = "📍";
}
// 可选：高亮显示当前位置
let myLocationMarker = null;
function showUserLocationMarker(lnglat) {
  if(myLocationMarker) { map.remove(myLocationMarker);}
  myLocationMarker = new AMap.Marker({
    map: map,
    position: lnglat,
    offset: new AMap.Pixel(-13, -15),
    content: '<div style="width:26px;height:26px;border-radius:50%;background:#1976d244;border:2px solid #2196f3;box-shadow:0 1px 7px #2196f388;"></div>\
              <div style="position:absolute;left:8px;top:8px;width:10px;height:10px;background:#2196f3;border-radius:50%;"></div>'
  });
}
// ========== 可拖动弹窗功能 ==========
let currentPopup = null;
let isDragging = false;
let dragOffset = { x: 0, y: 0 };

// 显示可拖动弹窗
function showDraggablePopup(poi, lng, lat) {
  console.log('showDraggablePopup 被调用', poi.name, lng, lat); // 调试信息
  const popup = document.getElementById('draggable-popup');
  console.log('弹窗元素:', popup); // 调试信息

  if (!popup) {
    console.error('找不到弹窗元素！');
    return;
  }

  const nameElem = popup.querySelector('.popup-store-name');
  const addressElem = popup.querySelector('.popup-store-address');

  // 设置弹窗内容
  nameElem.textContent = poi.name || '未知店铺';
  addressElem.textContent = `地址：${poi.address || '地址信息不详'}`;

  // 将经纬度转换为屏幕坐标
  const pixel = map.lngLatToContainer([lng, lat]);
  console.log('屏幕坐标:', pixel); // 调试信息

  // 设置弹窗位置（在红点右侧显示）
  let left = pixel.x + 20;
  let top = pixel.y - 50;
  console.log('初始位置:', left, top); // 调试信息

  // 确保弹窗不超出屏幕边界
  const mapContainer = document.getElementById('amap-canvas');
  const mapRect = mapContainer.getBoundingClientRect();
  const popupWidth = 300; // 预估弹窗宽度
  const popupHeight = 120; // 预估弹窗高度

  // 右边界检查
  if (left + popupWidth > mapRect.width) {
    left = pixel.x - popupWidth - 20; // 显示在红点左侧
  }

  // 下边界检查
  if (top + popupHeight > mapRect.height) {
    top = mapRect.height - popupHeight - 10;
  }

  // 上边界检查
  if (top < 10) {
    top = 10;
  }

  // 左边界检查
  if (left < 10) {
    left = 10;
  }

  popup.style.left = left + 'px';
  popup.style.top = top + 'px';
  popup.style.display = 'block';
  console.log('弹窗已显示，最终位置:', left, top); // 调试信息
  console.log('弹窗样式:', popup.style.cssText); // 调试信息

  currentPopup = popup;
}

// 隐藏弹窗
function hideDraggablePopup() {
  const popup = document.getElementById('draggable-popup');
  popup.style.display = 'none';
  currentPopup = null;
  isDragging = false;
}

// 拖动功能实现
function initDragFunctionality() {
  console.log('初始化拖动功能开始');
  const popup = document.getElementById('draggable-popup');
  console.log('弹窗元素:', popup);

  if (!popup) {
    console.error('初始化失败：找不到弹窗元素！');
    return;
  }

  const header = popup.querySelector('.popup-header');
  const closeBtn = popup.querySelector('.popup-close-btn');
  console.log('标题栏元素:', header);
  console.log('关闭按钮元素:', closeBtn);

  // 关闭按钮事件
  closeBtn.onclick = function(e) {
    e.stopPropagation();
    hideDraggablePopup();
  };

  // 开始拖动的通用函数
  function startDrag(clientX, clientY) {
    isDragging = true;
    const rect = popup.getBoundingClientRect();
    dragOffset.x = clientX - rect.left;
    dragOffset.y = clientY - rect.top;

    // 添加拖动样式
    popup.style.cursor = 'move';
    document.body.style.userSelect = 'none';
  }

  // 鼠标按下开始拖动
  header.onmousedown = function(e) {
    if (e.target === closeBtn) return; // 点击关闭按钮时不拖动
    e.preventDefault(); // 防止文本选择
    e.stopPropagation(); // 防止事件冒泡到地图
    startDrag(e.clientX, e.clientY);
  };

  // 触摸开始拖动（移动端支持）
  header.ontouchstart = function(e) {
    if (e.target === closeBtn) return;
    e.preventDefault(); // 防止触摸时的默认行为
    e.stopPropagation(); // 防止事件冒泡到地图
    const touch = e.touches[0];
    startDrag(touch.clientX, touch.clientY);
  };

  // 拖动移动的通用函数
  function handleDragMove(clientX, clientY) {
    if (!isDragging || !currentPopup) return;

    const mapContainer = document.getElementById('amap-canvas');
    const mapRect = mapContainer.getBoundingClientRect();

    let newLeft = clientX - dragOffset.x;
    let newTop = clientY - dragOffset.y;

    // 边界限制
    const popupRect = currentPopup.getBoundingClientRect();
    const popupWidth = popupRect.width;
    const popupHeight = popupRect.height;

    // 确保弹窗不超出地图容器边界
    if (newLeft < 0) newLeft = 0;
    if (newTop < 0) newTop = 0;
    if (newLeft + popupWidth > mapRect.width) newLeft = mapRect.width - popupWidth;
    if (newTop + popupHeight > mapRect.height) newTop = mapRect.height - popupHeight;

    currentPopup.style.left = newLeft + 'px';
    currentPopup.style.top = newTop + 'px';
  }

  // 鼠标移动时拖动弹窗
  document.onmousemove = function(e) {
    handleDragMove(e.clientX, e.clientY);
  };

  // 触摸移动时拖动弹窗（移动端支持）
  document.ontouchmove = function(e) {
    if (isDragging && e.touches.length > 0) {
      e.preventDefault(); // 防止页面滚动和地图拖动
      const touch = e.touches[0];
      handleDragMove(touch.clientX, touch.clientY);
    }
  };

  // 结束拖动的通用函数
  function endDrag() {
    if (isDragging) {
      isDragging = false;
      if (currentPopup) {
        currentPopup.style.cursor = '';
      }
      document.body.style.userSelect = '';
    }
  }

  // 鼠标释放结束拖动
  document.onmouseup = endDrag;

  // 触摸结束拖动（移动端支持）
  document.ontouchend = endDrag;
}

// 点击地图其他区域关闭弹窗
map.on('click', function(e) {
  console.log('地图被点击了', e); // 调试信息
  // 延迟执行，让红点的点击事件先执行
  setTimeout(function() {
    if (currentPopup && !isDragging) {
      console.log('关闭弹窗');
      hideDraggablePopup();
    }
  }, 10);
});

// 确保DOM加载完成后再初始化
document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM加载完成，开始初始化拖动功能');
  initDragFunctionality();
});

// 如果DOM已经加载完成，立即初始化
if (document.readyState === 'loading') {
  // DOM还在加载中，等待DOMContentLoaded事件
} else {
  // DOM已经加载完成，立即初始化
  console.log('DOM已加载完成，立即初始化拖动功能');
  initDragFunctionality();
}

// 初始化
firstSearchWithGeolocation();