<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>弹窗测试</title>
  <style>
    .test-dot {
      width: 20px;
      height: 20px;
      background: red;
      border-radius: 50%;
      cursor: pointer;
      margin: 50px;
      position: relative;
      z-index: 100;
    }
    
    .draggable-popup {
      position: absolute;
      z-index: 1000;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.15);
      border: 2px solid #b00;
      min-width: 250px;
      max-width: 350px;
      font-family: Arial, sans-serif;
      user-select: none;
    }

    .popup-header {
      background: #b00;
      color: #fff;
      padding: 8px 12px;
      border-radius: 6px 6px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: move;
      font-weight: bold;
    }

    .popup-close-btn {
      background: none;
      border: none;
      color: #fff;
      font-size: 18px;
      font-weight: bold;
      cursor: pointer;
      padding: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 3px;
      transition: background-color 0.2s;
    }

    .popup-content {
      padding: 12px;
      line-height: 1.6;
    }
  </style>
</head>
<body>
  <h1>弹窗测试</h1>
  <div class="test-dot" onclick="showTestPopup()"></div>
  
  <!-- 可拖动弹窗 -->
  <div id="draggable-popup" class="draggable-popup" style="display: none;">
    <div class="popup-header">
      <span class="popup-title">店铺信息</span>
      <button class="popup-close-btn" onclick="hideTestPopup()">&times;</button>
    </div>
    <div class="popup-content">
      <div class="popup-store-name">测试店铺</div>
      <div class="popup-store-address">地址：测试地址</div>
    </div>
  </div>

  <script>
    function showTestPopup() {
      console.log('显示测试弹窗');
      const popup = document.getElementById('draggable-popup');
      popup.style.left = '100px';
      popup.style.top = '100px';
      popup.style.display = 'block';
    }
    
    function hideTestPopup() {
      console.log('隐藏测试弹窗');
      const popup = document.getElementById('draggable-popup');
      popup.style.display = 'none';
    }
  </script>
</body>
</html>
