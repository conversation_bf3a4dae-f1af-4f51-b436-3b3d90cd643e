html, body {height: 100%;margin: 0; padding: 0; background: #fff; overflow-x: hidden;}
.a4-container {width: 794px;height: 1123px; margin: 20px auto; border: 1px solid #ccc; background: #fff;}
#amap-canvas {width: 100%; height: 1000px; position: relative; overflow: hidden;}

/* 移动端响应式布局 */
@media screen and (max-width: 768px) {
  html, body {
    overflow: hidden;
  }

  .a4-container {
    width: 100%;
    height: 100vh;
    margin: 0;
    border: none;
    overflow: hidden;
  }

  #amap-canvas {
    height: 100vh;
    width: 100vw;
  }

  /* 移动端搜索框优化 */
  .keyword-search-box {
    bottom: 10px;
    left: 10px;
    right: 10px;
    transform: none;
    max-width: none;
    width: calc(100% - 20px);
    padding: 8px 12px;
  }

  .keyword-input {
    width: 20px;
    font-size: 13px;
    padding: 4px 2px;
  }

  .range-label {
    font-size: 11px;
    width: 20px;
  }

  .range-input {
    width: 18px;
    font-size: 13px;
    padding: 4px 1px;
  }

  .search-btn {
    font-size: 13px;
    padding: 6px 8px;
    min-width: 50px;
  }

  /* 移动端按钮优化 */
  .refresh-btn {
    left: 10px;
    top: 10px;
    font-size: 14px;
    padding: 8px 16px;
  }

  .countdown-hint {
    left: 10px;
    top: 60px;
    font-size: 13px;
    max-width: calc(100% - 20px);
  }

  .my-location-btn {
    right: 5px;
    bottom: 140px;
    width: 50px;
    height: 50px;
    font-size: 24px;
  }

  /* 移动端标题隐藏，避免与搜索框重叠 */
  .a4-container > div:first-child,
  .a4-container > div:nth-child(2) {
    display: none;
  }
}
.refresh-btn {position:absolute;left:18px;top:17px;z-index:102;background:#f55;color:#fff;font-size:17px;font-weight:bold;border-radius:5px;border:0;cursor:pointer;padding:6px 20px;box-shadow: 0 2px 8px #b00a;transition: background .15s;max-width: 160px;}
.refresh-btn:hover { background: #d22;}
.countdown-hint {position: absolute; left:190px;top:24px;z-index:103;color: #919; font-size:15px; font-weight:bold;background: #fff4; border-radius: 3px; padding: 2px 10px;pointer-events: none; transition: opacity 0.2s;max-width: 340px; overflow:hidden; text-overflow:ellipsis; white-space:nowrap;}
/* 搜索框基础样式 */
.keyword-search-box {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
  background: rgba(255,255,255,0.94);
  border-radius: 7px;
  box-shadow: 0 2px 14px rgba(187,187,187,0.5);
  padding: 8px 12px;
  max-width: 96%;
  display: table;
  table-layout: auto;
  border-spacing: 6px;
}

/* 统一的一行布局 - 所有元素都使用table-cell */
.keyword-input,
.range-label,
.range-input,
.search-btn {
  display: table-cell;
  vertical-align: middle;
  white-space: nowrap;
}

/* 移动端搜索框优化 */
.keyword-search-box {
  bottom: 10px;
  left: 50%;                    /* 改为居中定位 */
  transform: translateX(-50%);  /* 改为居中定位 */
  max-width: 96%;              /* 与桌面端一致 */
  width: auto;                 /* 改为自适应宽度 */
  padding: 8px 12px;           /* 与桌面端一致 */
}

.keyword-input {
  width: 60px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 2px 3px;
  outline: none;
  box-sizing: border-box;
  text-align: center;
}

.range-label {
  font-size: 12px;
  color: #666;
  text-align: center;
  width: 20px;
}

.range-input {
  width: 30px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 2px 1px;
  outline: none;
  text-align: center;
  box-sizing: border-box;
}

.search-btn {
  background: #44a;
  color: #fff;
  border: 0;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  padding: 6px 12px;
  cursor: pointer;
  transition: background .15s;
  min-width: 60px;
}
.search-btn:hover {background:#206;}
.my-red-dot {width: 16px;height: 16px; background: rgba(255,0,0,0.85);border:2px solid #fff; border-radius: 50%;box-shadow: 0 0 7px #900; cursor: pointer; transition:transform .15s;position: absolute;top: 0; left: 0;will-change: transform;}
.my-red-dot:hover { transform: scale(1.2);}
.info-window {line-height: 1.6; min-width: 180px; max-width: 280px;padding: 8px 12px; background: #fff;border-radius: 7px; border:1.5px solid #b00;box-shadow:0 2px 16px #8002; color:#111; font-size: 14px;}
.amap-marker-label {z-index: 20;}
.my-location-btn {position:absolute; right:28px; bottom:28px; z-index:200;width:46px;height:46px;background:rgba(255,255,255,0.96);color:#1976d2;border-radius:50%;border:2px solid #eee;font-size:29px;cursor:pointer;box-shadow: 0 2px 12px #aaa4;outline:none;transition:transform 0.14s, box-shadow 0.18s;display:flex;align-items:center;justify-content:center;}
.my-location-btn:hover {background: #e0f0ff;box-shadow: 0 2px 18px #6cf6;transform: scale(1.08);}
@media print { .info-window, .refresh-btn, .my-location-btn, .countdown-hint, .keyword-search-box { display:none!important;}}